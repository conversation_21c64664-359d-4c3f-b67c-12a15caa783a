import { Close, KeyboardArrowLeft } from "@mui/icons-material";
import {
    AppBar,
    Box,
    Button,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Skeleton,
    styled,
    useTheme,
    Snackbar,
    TextField,
    FormControlLabel,
    Radio,
    RadioGroup
} from "@mui/material";
import React, { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import PropTypes from "prop-types";
import MuiAlert from "@mui/material/Alert";
import httpclient from "../../../../Utils";
import debounce from "lodash.debounce";
import VariantsTable from "../../../../Components/VariantsTable";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "249px",
    maxWidth: "250px",
    fontWeight: "600",
}));

const Values = styled("div")(({ theme }) => ({
    marginLeft: "10px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    // console.log(props);
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}


const ViewProductDetail = (props) => {
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [mappingDetails, setMappingDetails] = useState("");
    const [shopifyParentId, setShopifyParentId] = useState("");

    const [showSearchUI, setShowSearchUI] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [variantSearchQuery, setVariantSearchQuery] = useState("");
    const [searchResults, setSearchResults] = useState([]);
    const [variantSearchResults, setVariantSearchResults] = useState([]);
    const [selectedProductId, setSelectedProductId] = useState(null);
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [variantConfirmOpen, setVariantConfirmOpen] = useState(false);
    const [loading, setLoading] = useState(true);
    const [searchLoading, setSearchLoading] = useState(false);
    const [variantSearchLoading, setVariantSearchLoading] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [forceMapConfirmOpen, setForceMapConfirmOpen] = useState(false);
    const [forceMapMessage, setForceMapMessage] = useState("");
    const [isMatrixMapping, setIsMatrixMapping] = useState(false);

    const [detailsData, setDetailsData] = useState([]);

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };


    const [dialogDetails, setDialogDetails] = useState({
        open: true,
    });

    useEffect(() => {
        props.sendDetails(dialogDetails);
    }, [props, dialogDetails]);


    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const handleCloseSnack = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    const getProductDetails = async (productId) => {
        try {
            setLoading(true);
            const response = await httpclient.get(`request-response?requestName=erply/v2/products/${productId}&serviceType=shopify`);
            if (response.data) {
                console.log(response.data.data);
                setDetailsData(response.data.data);
                setShopifyParentId(response.data.data.shopify_product?.shopify_products_string_id || "");
            }
        } catch (err) {
            console.log(err);
        } finally {
            setLoading(false);
        }
    }

    // Load mapping data on component mount
    useEffect(() => {
        if (props.viewDetails?.productID) {
            getProductDetails(props.viewDetails.productID);
        }
    }, [props.viewDetails?.productID])


    const performSearch = async (query) => {
        try {
            setSearchLoading(true);
            const [titleRes, handleRes] = await Promise.all([
                httpclient.get(
                    `/request-response?requestName=shopify/v2/products&filters[title][$contains]=${query}`
                ),
                httpclient.get(
                    `/request-response?requestName=shopify/v2/products&filters[handle][$contains]=${query}`
                ),
            ]);

            const titleResults = titleRes.data.data || [];
            const handleResults = handleRes.data.data || [];

            const combinedResults = [
                ...titleResults,
                ...handleResults.filter(
                    (item) => !titleResults.some((titleItem) => titleItem.id === item.id)
                ),
            ];

            setSearchResults(combinedResults);
        } catch (error) {
            console.error("Search failed:", error);
            setSearchResults([]);
        } finally {
            setSearchLoading(false);
        }
    };

    // Debounce search
    const debouncedSearch = debounce((query) => {
        if (query.trim()) performSearch(query);
        else setSearchResults([]);
    }, 500);

    useEffect(() => {
        debouncedSearch(searchQuery);
        return debouncedSearch.cancel;
    }, [searchQuery]);

    const performVariantSearch = async (query, shopifyParentId = '') => {
        // console.log({ "parentProductID": parentProductID });
        try {
            setVariantSearchLoading(true);
            const response = await httpclient.get(
                `request-response?requestName=shopify/v2/products&productType=Variation&filters[shopify_products_string_id][$eq]=${shopifyParentId}&filters[$or][0][displayName][$contains]=${query}&filters[$or][1][sku][$contains]=${query}&filters[$or][2][barcode][$contains]=${query}`
            );

            const combinedResults = response.data.data || [];

            setVariantSearchResults(combinedResults);
        } catch (error) {
            console.error("Variant search failed:", error);
            setVariantSearchResults([]);
        } finally {
            setVariantSearchLoading(false);
        }
    };

    // Debounce variant search
    const debouncedVariantSearch = debounce((query, shopifyParentId) => {
        if (query.trim()) performVariantSearch(query, shopifyParentId);
        else setVariantSearchResults([]);
    }, 500);

    useEffect(() => {
        debouncedVariantSearch(variantSearchQuery, shopifyParentId);
        return debouncedVariantSearch.cancel;
    }, [variantSearchQuery, shopifyParentId]);


    const handleMatrixMap = async () => {
        setIsMatrixMapping(true);
        try {
            const res = await httpclient.post(`/request-response?requestName=erply/v2/mapping/v1/product`, {
                erplyProductID: detailsData.productID,
                shopifyProductID: selectedProductId,
            });

            // Refresh data after successful mapping
            getProductDetails(detailsData.productID);

            setMessage(res.data.message);
            setMessageState("success");
            setOpen(true);
            setIsMatrixMapping(false)
            setConfirmOpen(false);
            setShowSearchUI(false);
            setSearchQuery("")
        } catch (err) {

            setOpen(true);
            setMessageState("error");
            setMessage(err.response.data.message);
            setIsMatrixMapping(false)
            setConfirmOpen(false);
        }
    };

    const getVariantMappingData = async (variantId) => {
        try {
            const res = await httpclient.get(`request-response?requestName=erply/v2/products/${variantId}&serviceType=shopify&productType=Variation`);
            return res.data.data;
        } catch (err) {
            setMessage(err.response.data.message);
            setMessageState("error");
            setOpen(true);
            return null;
        }
    }

    const handleMapping = async (row) => {
        const mappingData = await getVariantMappingData(row.productID);
        console.log(mappingData);
        setMappingDetails({
            ...row,
            mappingData: mappingData
        });
    };

    const handleBackFromMapping = () => {
        setMappingDetails("");
        setVariantSearchQuery("")
        setVariantSearchResults([])
    };

    const handleVariantMap = async () => {
        console.log("Mapping variant");
        console.log(mappingDetails.productID);
        console.log(selectedProductId);
        try {
            const res = await httpclient.post(`/request-response?requestName=erply/v2/mapping/v1/product&productType=Variation`, {
                erplyProductID: mappingDetails.productID,
                shopifyProductID: selectedProductId,
            });

            // Refresh variant mapping data
            const updatedMappingData = await getVariantMappingData(mappingDetails.productID);
            setMappingDetails({
                ...mappingDetails,
                mappingData: updatedMappingData
            });

            setVariantConfirmOpen(false);
            setShowSearchUI(false);
            setMessageState("success");
            setMessage(res.data.message);
            setOpen(true);
        } catch (err) {
            if (err.response?.status === 404 &&
                err.response?.data?.message === "Parent Product Mapping ID mismatch between Erply and Shopify. Would you like to proceed with forced mapping?") {
                setForceMapMessage(err.response.data.message);
                setVariantConfirmOpen(false);
                setForceMapConfirmOpen(true);
            }
        }
    };

    const handleForceVariantMap = async () => {
        try {
            const res = await httpclient.post(`/request-response?requestName=erply/v2/mapping/v1/product&productType=Variation&isForceMapped=1`, {
                erplyProductID: mappingDetails.productID,
                shopifyProductID: selectedProductId,
            });

            // Refresh variant mapping data
            const updatedMappingData = await getVariantMappingData(mappingDetails.productID);
            setMappingDetails({
                ...mappingDetails,
                mappingData: updatedMappingData
            });

            setForceMapConfirmOpen(false);
            setShowSearchUI(false);
            setMessageState("success");
            setMessage(res.data.message);
            setOpen(true);
        } catch (err) {
            setMessageState("error");
            setMessage(err.response.data.message);
            setOpen(true);
        }
    };

    const checkParentMapping=()=>{
        console.log(shopifyParentId);
        if(shopifyParentId!== ""){
            setShowSearchUI(true)
        }else{
            setMessageState("error");
            setMessage("Matrix mapping is required before mapping variants.");
            setOpen(true);
        }
    }

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    <div>
                        View Product Mapping Details{" "}
                        {"(" +
                            (detailsData.name || "-") +
                            ")"}
                    </div>
                    <IconButton onClick={handleClose}>
                        <Close />
                    </IconButton>
                </StyledHeaderTitle>
                {loading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: "0" }}>
                        <AppBarTabs position="static">
                            <Tabs
                                value={value}
                                onChange={handleChange}
                                indicatorColor="secondary"
                                textColor="inherit"
                                variant="fullWidth"
                                aria-label="full width tabs example"
                            >
                                <Tab label="Details" {...a11yProps(0)} />
                                <Tab label="Variants" {...a11yProps(1)} />
                            </Tabs>
                        </AppBarTabs>

                        <TabPanel value={value} index={0} dir={theme.direction}>
                            <Box>
                                <Grid container spacing={2}>
                                    {/* Column Headings */}
                                    <Grid item xs={12} md={6}>
                                        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                                            Erply Product Details
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                                            Shopify Product Info
                                        </Typography>
                                    </Grid>

                                    {/* Left Column - Erply Product Details */}
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>ERPLY Product ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{detailsData?.productID || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Name</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{detailsData?.name || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>External ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{detailsData?.externalID || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Price(with VAT)</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{`$${parseFloat(detailsData?.price).toFixed(2)}` || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Code</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{detailsData?.code || "-"}</Values>
                                        </FlexContent>
                                    </Grid>

                                    {/* Right Column - Shopify Product Info */}
                                    <Grid item xs={12} md={6}>
                                        {detailsData?.shopify_product ? (
                                            <>
                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Shopify Product ID</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{detailsData?.shopify_product?.shopify_products_string_id || "-"}</Values>
                                                </FlexContent>

                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Product Name</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{detailsData?.shopify_product?.title || "-"}</Values>
                                                </FlexContent>

                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Product Handle</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{detailsData?.shopify_product?.handle || "-"}</Values>
                                                </FlexContent>

                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Product Shopify URL</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{detailsData?.shopify_product?.shopifyURL ? (
                                                        <a
                                                            href={detailsData.shopify_product.shopifyURL}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            style={{ color: "#3b82f6", textDecoration: "underline" }}
                                                        >
                                                            {detailsData.shopify_product.shopifyURL}
                                                        </a>
                                                    ) : (
                                                        "-"
                                                    )}</Values>
                                                </FlexContent>

                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Status</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{detailsData?.shopify_product?.status || "-"}</Values>
                                                </FlexContent>
                                            </>
                                        ) : (
                                            <Typography variant="h6" color="textSecondary" sx={{ mb: 2 }}>
                                                No product mapping found.
                                            </Typography>
                                        )}
                                    </Grid>

                                    {/* Mapping Button Row */}
                                    <Grid item xs={12}>
                                        <Box mt={2}>
                                            {!loading && (
                                                !detailsData.shopify_product ? (
                                                    <Button variant="contained" color="primary" onClick={() => setShowSearchUI(true)}>
                                                        Map Product
                                                    </Button>
                                                ) : (
                                                    <Button variant="outlined" onClick={() => setShowSearchUI(true)}>
                                                        Re-map Product
                                                    </Button>
                                                )
                                            )}
                                        </Box>
                                    </Grid>

                                    {/* Search UI Row */}
                                    {showSearchUI && (
                                        <Grid item xs={12}>
                                            <Box mt={2}>
                                                <TextField
                                                    label="Search product by Title or Handle"
                                                    fullWidth
                                                    variant="outlined"
                                                    value={searchQuery}
                                                    onChange={(e) => setSearchQuery(e.target.value)}
                                                />
                                                {searchLoading && <CircularProgress size={20} sx={{ mt: 2 }} />}

                                                {!searchLoading && searchResults.length > 0 && (
                                                    <RadioGroup
                                                        value={selectedProductId}
                                                        onChange={(e) => {
                                                            const selected = searchResults.find(
                                                                (p) => p.shopify_products_string_id === e.target.value
                                                            );
                                                            setSelectedProduct(selected);
                                                            setSelectedProductId(e.target.value);
                                                            setConfirmOpen(true);
                                                        }}
                                                        sx={{ mt: 2 }}
                                                    >
                                                        {searchResults.map((product) => (
                                                            <Box
                                                                key={product.id}
                                                                sx={{
                                                                    p: 2,
                                                                    border: "1px solid #ccc",
                                                                    borderRadius: "6px",
                                                                    mb: 1,
                                                                    backgroundColor:
                                                                        selectedProductId == product.id ? "#f0f0f0" : "white",
                                                                }}
                                                            >
                                                                <FormControlLabel
                                                                    value={product.shopify_products_string_id}
                                                                    control={<Radio />}
                                                                    label={
                                                                        <Box>
                                                                            <Typography fontWeight="bold">{product.title}</Typography>
                                                                            <Typography variant="body2" color="textSecondary">
                                                                                ID: {product.shopify_products_string_id} | Handle: {product.handle}
                                                                            </Typography>
                                                                        </Box>
                                                                    }
                                                                />
                                                            </Box>
                                                        ))}
                                                    </RadioGroup>
                                                )}

                                                {!searchLoading && searchQuery.trim() && searchResults.length === 0 && (
                                                    <Typography variant="body2" color="textSecondary" sx={{ mt: 2, textAlign: "center" }}>
                                                        No matching product found.
                                                    </Typography>
                                                )}
                                            </Box>
                                        </Grid>
                                    )}
                                </Grid>
                            </Box>
                        </TabPanel>

                        {/*Variant mapping */}
                        <TabPanel value={value} index={1} dir={theme.direction}>
                            {mappingDetails ? (
                                <>
                                    <Box display={"flex"} justifyContent={"space-between"}>
                                        <h3>Variant Mapping Details</h3>
                                        <Button onClick={handleBackFromMapping}>
                                            <KeyboardArrowLeft fontSize="small" sx={{ marginRight: "5px" }} />
                                            <span>Back</span>
                                        </Button>
                                    </Box>

                                    {!mappingDetails.mappingData?.shopify_product ? (
                                        <>
                                            {/* Custom Variant Details Display - No Mapping */}
                                            <Grid container spacing={3}>
                                                {/* Erply Variant Details */}
                                                <Grid item xs={12} md={6}>
                                                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: 'primary.main' }}>
                                                        ERPLY VARIANT DETAILS
                                                    </Typography>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Product ID</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.productID || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Product Code</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.code || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Product Name</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.name || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Barcode</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.code2 || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Price (with VAT)</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>${Number(mappingDetails.priceWithVat || 0).toFixed(2)}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Category</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.categoryName || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Brand</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.brandName || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Status</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.status || "-"}</Values>
                                                    </FlexContent>
                                                </Grid>

                                                {/* Right Column - No Mapping Found */}
                                                <Grid item xs={12} md={6}>
                                                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: 'secondary.main' }}>
                                                        SHOPIFY VARIANT DETAILS
                                                    </Typography>
                                                    <Typography variant="h6" color="textSecondary" sx={{ mb: 2 }}>
                                                        No variant mapping found.
                                                    </Typography>
                                                </Grid>
                                            </Grid>

                                            <Box mt={3}>
                                                <Button variant="contained" color="primary" onClick={checkParentMapping}>
                                                    Map Variant
                                                </Button>
                                            </Box>

                                            {/* Search UI for variants */}
                                            {showSearchUI && (
                                                <Box mt={2}>
                                                    <TextField
                                                        label="Search variant by Display Name or SKU or Barcode"
                                                        fullWidth
                                                        variant="outlined"
                                                        onChange={(e) => {
                                                            console.log({ "mappingDetails": mappingDetails });
                                                            setVariantSearchQuery(e.target.value);
                                                            debouncedVariantSearch(e.target.value, shopifyParentId);
                                                        }}
                                                    />
                                                    {loading && <CircularProgress size={20} sx={{ mt: 2 }} />}

                                                    {!loading && variantSearchResults.length > 0 && (
                                                        <RadioGroup
                                                            value={selectedProductId}
                                                            onChange={(e) => {
                                                                const selected = variantSearchResults.find(
                                                                    (p) => p.shopifyVariantId === e.target.value
                                                                );
                                                                setSelectedProduct(selected);
                                                                setSelectedProductId(e.target.value);
                                                                setVariantConfirmOpen(true);
                                                            }}
                                                            sx={{ mt: 2 }}
                                                        >
                                                            {variantSearchResults.map((product) => (
                                                                <Box
                                                                    key={product.id}
                                                                    sx={{
                                                                        p: 2,
                                                                        border: "1px solid #ccc",
                                                                        borderRadius: "6px",
                                                                        mb: 1,
                                                                        backgroundColor:
                                                                            selectedProductId === product.shopifyVariantId ? "#f0f0f0" : "white",
                                                                        cursor: "pointer",
                                                                        "&:hover": {
                                                                            backgroundColor: "#f5f5f5",
                                                                        },
                                                                    }}
                                                                    onClick={() => {
                                                                        setSelectedProduct(product);
                                                                        setSelectedProductId(product.shopifyVariantId);
                                                                        setVariantConfirmOpen(true);
                                                                    }}
                                                                >
                                                                    <FormControlLabel
                                                                        value={product.shopifyVariantId}
                                                                        control={<Radio />}
                                                                        label={
                                                                            <Box>
                                                                                <Typography fontWeight="bold">{product.displayName}</Typography>
                                                                                <Typography variant="body2" color="textSecondary">
                                                                                    SKU: {product.sku} | Size: {product.size} | Color: {product.color}
                                                                                </Typography>
                                                                            </Box>
                                                                        }
                                                                    />
                                                                </Box>
                                                            ))}
                                                        </RadioGroup>
                                                    )}

                                                    {!variantSearchLoading && variantSearchQuery.trim() && variantSearchResults.length === 0 && (
                                                        <Typography variant="body2" color="textSecondary" sx={{ mt: 2, textAlign: "center" }}>
                                                            No matching variant found.
                                                        </Typography>
                                                    )}
                                                </Box>
                                            )}
                                        </>
                                    ) : (
                                        <>
                                            {/* Custom Variant Details Display */}
                                            <Grid container spacing={3}>
                                                {/* Erply Variant Details */}
                                                <Grid item xs={12} md={6}>
                                                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: 'primary.main' }}>
                                                        ERPLY VARIANT DETAILS
                                                    </Typography>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Product ID</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.productID || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Product Code</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.code || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Product Name</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.name || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Barcode</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.code2}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Price (with VAT)</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>${Number(mappingDetails.priceWithVat || 0).toFixed(2)}</Values>
                                                    </FlexContent>



                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Category</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.categoryName || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Brand</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.brandName || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Status</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.status || "-"}</Values>
                                                    </FlexContent>
                                                </Grid>

                                                {/* Shopify Variant Details */}
                                                <Grid item xs={12} md={6}>
                                                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: 'secondary.main' }}>
                                                        SHOPIFY VARIANT DETAILS
                                                    </Typography>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Shopify ID</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.shopifyVariantId || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>SKU</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.sku || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Display Name</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.displayName || "-"}</Values>
                                                    </FlexContent>



                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Barcode</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.barcode || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Price</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>${Number(mappingDetails.mappingData.shopify_product?.price || 0).toFixed(2)}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Color</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.color || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Size</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.size || "-"}</Values>
                                                    </FlexContent>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Status</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.status || "-"}</Values>
                                                    </FlexContent>
                                                </Grid>
                                            </Grid>

                                            <Box mt={3}>
                                                <Button variant="outlined" onClick={() => setShowSearchUI(true)}>
                                                    Re-map Variant
                                                </Button>
                                            </Box>

                                            {/* Re-mapping Search UI */}
                                            {showSearchUI && (
                                                <Box mt={2}>
                                                    <TextField
                                                        label="Search variant by Display Name or SKU or Barcode"
                                                        fullWidth
                                                        variant="outlined"
                                                        value={variantSearchQuery}
                                                        onChange={(e) => {
                                                            setVariantSearchQuery(e.target.value);
                                                            debouncedVariantSearch(e.target.value, mappingDetails.parentProductID);
                                                        }}
                                                    />
                                                    {loading && <CircularProgress size={20} sx={{ mt: 2 }} />}

                                                    {!loading && variantSearchResults.length > 0 && (
                                                        <RadioGroup
                                                            value={selectedProductId}
                                                            onChange={(e) => {
                                                                const selected = variantSearchResults.find(
                                                                    (p) => p.shopifyVariantId === e.target.value
                                                                );
                                                                setSelectedProduct(selected);
                                                                setSelectedProductId(e.target.value);
                                                                setVariantConfirmOpen(true);
                                                            }}
                                                            sx={{ mt: 2 }}
                                                        >
                                                            {variantSearchResults.map((product) => (
                                                                <Box
                                                                    key={product.id}
                                                                    sx={{
                                                                        p: 2,
                                                                        border: "1px solid #ccc",
                                                                        borderRadius: "6px",
                                                                        mb: 1,
                                                                        backgroundColor:
                                                                            selectedProductId === product.shopifyVariantId ? "#f0f0f0" : "white",
                                                                        cursor: "pointer",
                                                                        "&:hover": {
                                                                            backgroundColor: "#f5f5f5",
                                                                        },
                                                                    }}
                                                                    onClick={() => {
                                                                        setSelectedProduct(product);
                                                                        setSelectedProductId(product.shopifyVariantId);
                                                                        setVariantConfirmOpen(true);
                                                                    }}
                                                                >
                                                                    <FormControlLabel
                                                                        value={product.shopifyVariantId}
                                                                        control={<Radio />}
                                                                        label={
                                                                            <Box>
                                                                                <Typography fontWeight="bold">{product.displayName}</Typography>
                                                                                <Typography variant="body2" color="textSecondary">
                                                                                    SKU: {product.sku} | Size: {product.size} | Color: {product.color}
                                                                                </Typography>
                                                                            </Box>
                                                                        }
                                                                    />
                                                                </Box>
                                                            ))}
                                                        </RadioGroup>
                                                    )}

                                                    {!loading && variantSearchQuery.trim() && variantSearchResults.length === 0 && (
                                                        <Typography variant="body2" color="textSecondary" sx={{ mt: 2, textAlign: "center" }}>
                                                            No matching variant found.
                                                        </Typography>
                                                    )}
                                                </Box>
                                            )}
                                        </>
                                    )}
                                </>
                            ) : (
                                <VariantsTable
                                    variants={detailsData?.erply_product_variants}
                                    handleMapping={handleMapping}
                                />
                            )}
                        </TabPanel>
                    </DialogContent>
                )}
                <DialogActions>
                    <Button onClick={handleClose} variant="outlined" color="primary">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Matrix Product Mapping Confirmation Dialog */}
            <Dialog open={confirmOpen} onClose={() => setConfirmOpen(false)}>
                <DialogTitle>Confirm Product Mapping</DialogTitle>
                <DialogContent>
                    <Typography>
                        Are you sure you want to map this product?
                    </Typography>
                    <Typography mt={1} fontWeight="bold">
                        {selectedProduct?.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                        ID: {selectedProduct?.shopify_products_string_id} | Handle: {selectedProduct?.handle}
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setConfirmOpen(false)}>Cancel</Button>
                    <Button variant="contained" onClick={handleMatrixMap} disabled={isMatrixMapping}>
                        Confirm
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Variant Mapping Confirmation Dialog */}
            <Dialog open={variantConfirmOpen} onClose={() => setVariantConfirmOpen(false)}>
                <DialogTitle>Confirm Variant Mapping</DialogTitle>
                <DialogContent>
                    <Typography>
                        Are you sure you want to map this variant?
                    </Typography>
                    <Typography mt={1} fontWeight="bold">
                        {selectedProduct?.displayName}
                    </Typography>
                    <Typography variant="body2" mt={3}>
                        <span style={{ fontWeight: "bold" }}>SKU:</span> {selectedProduct?.sku}
                    </Typography>
                    <Typography variant="body2" mt={1}>
                        <span style={{ fontWeight: "bold" }}>Size:</span> {selectedProduct?.size}
                    </Typography>
                    <Typography variant="body2" mt={1}>
                        <span style={{ fontWeight: "bold" }}>Color:</span> {selectedProduct?.color}
                    </Typography>
                    <Typography variant="body2" mt={1}>
                        <span style={{ fontWeight: "bold" }}>BarCode:</span> {selectedProduct?.barcode}
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setVariantConfirmOpen(false)}>Cancel</Button>
                    <Button variant="contained" onClick={handleVariantMap}>
                        Confirm
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Force Map Confirmation Dialog */}
            <Dialog open={forceMapConfirmOpen} onClose={() => setForceMapConfirmOpen(false)}>
                <DialogTitle>Force Mapping Required</DialogTitle>
                <DialogContent>
                    <Typography>
                        {forceMapMessage}
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setForceMapConfirmOpen(false)}>No</Button>
                    <Button variant="contained" onClick={handleForceVariantMap}>
                        Yes
                    </Button>
                </DialogActions>
            </Dialog>

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleCloseSnack}
            >
                <Alert
                    onClose={handleCloseSnack}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ViewProductDetail;

