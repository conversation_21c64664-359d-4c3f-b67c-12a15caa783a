import React from "react";
import { styled } from "@mui/material/styles";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  IconButton,
  Skeleton,
  TableFooter,
  TablePagination,
  useTheme,
  tableCellClasses,
  Chip,
  Link,
} from "@mui/material";
import {
  KeyboardArrowDown,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  KeyboardArrowUp,
  UnfoldMore,
  ExpandMore,
  ExpandLess,
  Visibility,
} from "@mui/icons-material";
import moment from "moment";
import PropTypes from "prop-types";

// Styled components
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.light,
    fontWeight: 600,
    fontSize: "0.875rem",
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
  "& svg": {
    position: "relative",
    top: "5px",
    cursor: "pointer",
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:last-child td, &:last-child th": {
    border: 0,
  },
  "&:hover": {
    backgroundColor: theme.palette.action.selected,
    cursor: "pointer",
  },
}));

const ParentRow = styled(StyledTableRow)(({ theme }) => ({
  backgroundColor: "#f5f5f5 !important",
  fontWeight: "600",
  "&:hover": {
    backgroundColor: "#e8e8e8 !important",
  },
}));

const ChildRow = styled(StyledTableRow)(({ theme }) => ({
  backgroundColor: "#fafafa !important",
  "& td": {
    paddingLeft: "40px",
    fontSize: "13px",
  },
  "&:hover": {
    backgroundColor: "#f0f0f0 !important",
  },
}));

const NoDataContainer = styled(TableRow)({
  position: "relative",
  height: "50px",
});

const NoDataCell = styled(TableCell)({
  position: "absolute",
  right: "50%",
  borderBottom: "none",
  textAlign: "center",
  color: "#666",
  fontStyle: "italic",
});

// Table pagination actions component
function TablePaginationActions({ count, page, rowsPerPage, onPageChange }) {
  const theme = useTheme();

  const handleBackButtonClick = (event) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event) => {
    onPageChange(event, page + 1);
  };

  return (
    <div style={{ flexShrink: "0" }}>
      <IconButton
        onClick={handleBackButtonClick}
        disabled={page === 1}
        aria-label="previous page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowRight />
        ) : (
          <KeyboardArrowLeft />
        )}
      </IconButton>
      <IconButton
        onClick={handleNextButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage)}
        aria-label="next page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowLeft />
        ) : (
          <KeyboardArrowRight />
        )}
      </IconButton>
    </div>
  );
}

TablePaginationActions.propTypes = {
  count: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  page: PropTypes.number.isRequired,
  rowsPerPage: PropTypes.number.isRequired,
};

// Helper function to check if a row is mapped to Shopify
const isRowMapped = (row, isChild = false) => {
  if (isChild) {
    // For child variants, check multiple possible mapping indicators
    return (
      (row.shopifyVariantID && row.shopifyVariantID !== "") ||
      (row.shopify_product && row.shopify_product.shopifyVariantId && row.shopify_product.shopifyVariantId !== "")
    );
  } else {
    // For parent matrix products, check if they have shopifyProductID
    return row.shopifyProductID && row.shopifyProductID !== "";
  }
};

// Helper function to get Shopify product data
const   getShopifyData = (row, isChild = false, field) => {
  const isMapped = isRowMapped(row, isChild);
  if (!isMapped) {
    return "-";
  }

  const shopifyProduct = row.shopify_product;

  if (isChild) {
    // For child variants
    return shopifyProduct?.[field] || "-";
  } else {
    // For parent matrix products
    if (field === "shopify_products_string_id") {
      return shopifyProduct?.shopify_products_string_id || row.shopifyProductID || "-";
    }
    return shopifyProduct?.[field] || "-";
  }
};

// Cell content renderer
const renderCellContent = (row, column, isChild = false) => {
  const value = row[column.id];

  switch (column.id) {
    case "expand":
      if (isChild) return null;
      return row.type === "MATRIX" && row.erply_product_variants?.length > 0 ? (
        <IconButton size="small">
          <ExpandMore />
        </IconButton>
      ) : null;

    case "priceWithVat":
    case "price":
      return value ? `$${parseFloat(value).toFixed(2)}` : "-";

    case "status":
      return value || "-";

    case "type":
      return (
        <Chip
          label={value || "UNKNOWN"}
          color={value === "MATRIX" ? "primary" : "secondary"}
          size="small"
        />
      );

    case "shopifyProductId":
      return getShopifyData(row, isChild, "shopify_products_string_id");

    case "shopifyTitle":
      return getShopifyData(row, isChild, "title");

    case "shopifyHandle":
      return getShopifyData(row, isChild, "handle");

    case "shopifyStatus":
      return getShopifyData(row, isChild, "status");

    case "erplyBarcode":
      if (isChild) {
        // For child variants, show Erply barcode (code2)
        return row.code2 || "-";
      } else {
        // For parent matrix products, don't show barcode
        return "-";
      }

    case "shopifyBarcode":
      if (isChild) {
        // For child variants, show Shopify barcode
        return row.shopify_product?.barcode || "-";
      } else {
        // For parent matrix products, don't show barcode
        return "-";
      }

    case "erplySku":
      if (isChild) {
        // For child variants, show Erply SKU (could be code or another field)
        return row.code || "-";
      } else {
        // For parent matrix products, don't show SKU
        return "-";
      }

    case "shopifySku":
      if (isChild) {
        // For child variants, show Shopify SKU
        return row.shopify_product?.sku || "-";
      } else {
        // For parent matrix products, don't show SKU
        return "-";
      }

    case "name":
      if (isChild && row.variationDescription) {
        try {
          const variations = JSON.parse(row.variationDescription);
          const variationText = variations.map(v => `${v.name}: ${v.value}`).join(", ");
          return (
            <Box>
              <div style={{ fontWeight: "500" }}>{value}</div>
              <div style={{ fontSize: "12px", color: "#666" }}>{variationText}</div>
            </Box>
          );
        } catch (e) {
          return value || "-";
        }
      }
      return value || "-";

    default:
      return value || "-";
  }
};

// Main table component
const MatrixProductTable = ({
  columns = [],
  rows = [],
  loading = false,
  sortable = true,
  currentColumn = "",
  direction = false,
  page = 1,
  total = 0,
  fromTable = 0,
  toTable = 0,
  rowsPerPage = 20,
  onSort,
  onChangePage,
  onChangeRowsPerPage,
  expandedRows = new Set(),
  setExpandedRows,
  showChildrenAlso = false,
}) => {
  const handleRowExpand = (productID) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(productID)) {
      newExpandedRows.delete(productID);
    } else {
      newExpandedRows.add(productID);
    }
    setExpandedRows(newExpandedRows);
  };

  // Filter and prepare data based on showChildrenAlso
  const prepareTableData = () => {
    let tableData = [];

    rows.forEach((row) => {
      const isMatrix = row.type === "MATRIX";
      const hasVariants = row.erply_product_variants && row.erply_product_variants.length > 0;
      const isExpanded = expandedRows.has(row.productID);

      // Always show parent rows
      tableData.push({
        ...row,
        isParent: isMatrix,
        isChild: false,
      });

      // Show children only if showChildrenAlso is enabled AND (expanded or showChildrenAlso forces all to show)
      if (showChildrenAlso && hasVariants && isExpanded) {
        row.erply_product_variants.forEach((variant) => {
          tableData.push({
            ...variant,
            isParent: false,
            isChild: true,
            parentProductID: row.productID,
          });
        });
      }
    });

    return tableData;
  };

  const tableData = prepareTableData();

  // Render matrix products header row
  const renderMatrixHeader = () => (
    <TableRow style={{ backgroundColor: "#f0f0f0" }}>
      <StyledTableCell
        colSpan={showChildrenAlso ? columns.length : columns.filter(col => !["expand", "erplyBarcode", "shopifyBarcode", "erplySku", "shopifySku"].includes(col.id)).length}
        style={{
          fontWeight: "bold",
          fontSize: "16px",
          textAlign: "center",
          backgroundColor: "#e3f2fd",
          color: "#1976d2"
        }}
      >
        Matrix Products
      </StyledTableCell>
    </TableRow>
  );

  // Render variants header row
  const renderVariantsHeader = () => (
    <TableRow style={{ backgroundColor: "#f0f0f0" }}>
      <StyledTableCell
        colSpan={columns.length}
        style={{
          fontWeight: "bold",
          fontSize: "16px",
          textAlign: "center",
          backgroundColor: "#e8f5e8",
          color: "#2e7d32"
        }}
      >
        Product Variants
      </StyledTableCell>
    </TableRow>
  );

  // Render main table header with all columns
  const renderMainTableHeader = () => (
    <TableHead>
      <TableRow>
        {(showChildrenAlso ? columns : columns.filter(col => !["expand", "erplyBarcode", "shopifyBarcode", "erplySku", "shopifySku"].includes(col.id))).map((column) => (
          <StyledTableCell
            key={column.id}
            onClick={sortable && onSort && !["expand", "type", "erplyBarcode", "shopifyBarcode", "erplySku", "shopifySku", "shopifyProductId", "shopifyTitle", "shopifyHandle", "shopifyStatus"].includes(column.id) ? () => onSort(column.id) : undefined}
            style={{
              cursor: sortable && onSort && !["expand", "type", "erplyBarcode", "shopifyBarcode", "erplySku", "shopifySku", "shopifyProductId", "shopifyTitle", "shopifyHandle", "shopifyStatus"].includes(column.id) ? "pointer" : "default",
              width: column.id === "expand" ? "50px" : "auto",
              borderLeft: column.id === "shopifyProductId" ? "3px solid #8c8c8c" : "none"
            }}
          >
            {column.name}
            {sortable && onSort && !["expand", "type", "erplyBarcode", "shopifyBarcode", "erplySku", "shopifySku", "shopifyProductId", "shopifyTitle", "shopifyHandle", "shopifyStatus"].includes(column.id) && (
              <>
                {currentColumn === column.id ? (
                  direction ? (
                    <KeyboardArrowUp fontSize="small" />
                  ) : (
                    <KeyboardArrowDown fontSize="small" />
                  )
                ) : (
                  <UnfoldMore fontSize="small" />
                )}
              </>
            )}
          </StyledTableCell>
        ))}
      </TableRow>
    </TableHead>
  );

  // Render loading skeleton
  const renderLoadingSkeleton = () => {
    const columnsToShow = showChildrenAlso ? columns : columns.filter(col => !["expand", "erplyBarcode", "shopifyBarcode", "erplySku", "shopifySku"].includes(col.id));

    return (
      <TableBody>
        {Array.from({ length: 7 }).map((_, rowIndex) => (
          <TableRow key={rowIndex}>
            {columnsToShow.map((column) => (
              <StyledTableCell key={column.id}>
                <Skeleton variant="text" height={40} />
              </StyledTableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    );
  };

  // Group data by type for separate headers
  const matrixProducts = tableData.filter(row => row.isParent);
  const variantProducts = tableData.filter(row => row.isChild);

  return (
    <Paper sx={{ width: "100%", overflow: "hidden", marginBottom: "40px" }}>
      <TableContainer sx={{ maxHeight: 678 }}>
        <Table stickyHeader aria-label="matrix products table" sx={{ minWidth: 700 }}>
          {renderMainTableHeader()}
          {loading ? (
            renderLoadingSkeleton()
          ) : (
            <TableBody>
              {tableData.length > 0 ? (
                <>
                  {/* Matrix Products Section */}
                  {matrixProducts.length > 0 && (
                    <>
                      {renderMatrixHeader()}
                      {matrixProducts.map((row, rowIndex) => {
                        const RowComponent = ParentRow;

                        return (
                          <RowComponent
                            key={`matrix-${row.productID}-${rowIndex}`}
                            onClick={() => {
                              if (row.type === "MATRIX") {
                                handleRowExpand(row.productID);
                              }
                            }}
                            sx={{
                              backgroundColor: (() => {
                                const isMapped = isRowMapped(row, false);
                                if (isMapped) {
                                  return "#d4edda !important";
                                }
                                return undefined;
                              })(),
                            }}
                          >
                            {(showChildrenAlso ? columns : columns.filter(col => !["expand", "erplyBarcode", "shopifyBarcode", "erplySku", "shopifySku"].includes(col.id))).map((column, colIndex) => (
                              <StyledTableCell
                                key={column.id}
                                component={colIndex === 1 ? "th" : undefined}
                                scope={colIndex === 1 ? "row" : undefined}
                                onClick={(e) => {
                                  if (column.id === "expand" && row.type === "MATRIX") {
                                    e.stopPropagation();
                                    handleRowExpand(row.productID);
                                  }
                                }}
                                style={{
                                  borderLeft: column.id === "shopifyProductId" ? "3px solid #8c8c8c" : "none"
                                }}
                              >
                                {column.id === "expand" && row.type === "MATRIX" ? (
                                  <IconButton size="small">
                                    {expandedRows.has(row.productID) ? <ExpandLess /> : <ExpandMore />}
                                  </IconButton>
                                ) : (
                                  renderCellContent(row, column, false)
                                )}
                              </StyledTableCell>
                            ))}
                          </RowComponent>
                        );
                      })}
                    </>
                  )}

                  {/* Variants Section */}
                  {showChildrenAlso && variantProducts.length > 0 && (
                    <>
                      {renderVariantsHeader()}
                      {variantProducts.map((row, rowIndex) => {
                        const RowComponent = ChildRow;

                        return (
                          <RowComponent
                            key={`variant-${row.productID}-${rowIndex}`}
                            sx={{
                              backgroundColor: (() => {
                                const isMapped = isRowMapped(row, true);
                                if (isMapped) {
                                  return "#e8f5e8 !important";
                                }
                                return undefined;
                              })(),
                            }}
                          >
                            {columns.map((column, colIndex) => (
                              <StyledTableCell
                                key={column.id}
                                component={colIndex === 1 ? "th" : undefined}
                                scope={colIndex === 1 ? "row" : undefined}
                                style={{
                                  borderLeft: column.id === "shopifyProductId" ? "3px solid #8c8c8c" : "none"
                                }}
                              >
                                {renderCellContent(row, column, true)}
                              </StyledTableCell>
                            ))}
                          </RowComponent>
                        );
                      })}
                    </>
                  )}
                </>
              ) : (
                <NoDataContainer>
                  <NoDataCell colSpan={showChildrenAlso ? columns.length : columns.filter(col => !["expand", "erplyBarcode", "shopifyBarcode", "erplySku", "shopifySku"].includes(col.id)).length}>
                    No products found
                  </NoDataCell>
                </NoDataContainer>
              )}
            </TableBody>
          )}
          <TableFooter>
            <TableRow>
              <TablePagination
                rowsPerPageOptions={[20, 50, 70, 100]}
                rowsPerPage={rowsPerPage}
                page={page}
                count={total}
                slotProps={{ select: { native: true } }}
                labelDisplayedRows={() =>
                  `${fromTable || 0} - ${toTable || 0} of ${total}`
                }
                onPageChange={onChangePage}
                onRowsPerPageChange={onChangeRowsPerPage}
                ActionsComponent={TablePaginationActions}
              />
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
    </Paper>
  );
};

MatrixProductTable.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
    })
  ).isRequired,
  rows: PropTypes.array.isRequired,
  loading: PropTypes.bool,
  sortable: PropTypes.bool,
  currentColumn: PropTypes.string,
  direction: PropTypes.bool,
  page: PropTypes.number,
  total: PropTypes.number,
  fromTable: PropTypes.number,
  toTable: PropTypes.number,
  rowsPerPage: PropTypes.number,
  onSort: PropTypes.func,
  onChangePage: PropTypes.func,
  onChangeRowsPerPage: PropTypes.func,
  expandedRows: PropTypes.instanceOf(Set),
  setExpandedRows: PropTypes.func,
  showChildrenAlso: PropTypes.bool,
};

export default MatrixProductTable;
