import React from "react";
import { styled } from "@mui/material/styles";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  IconButton,
  Skeleton,
  TableFooter,
  TablePagination,
  useTheme,
  tableCellClasses,
  Chip,
  Link,
  Collapse,
} from "@mui/material";
import {
  KeyboardArrowDown,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  KeyboardArrowUp,
  UnfoldMore,
  ExpandMore,
  ExpandLess,
  Visibility,
} from "@mui/icons-material";
import moment from "moment";
import PropTypes from "prop-types";

// Styled components
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.light,
    fontWeight: 600,
    fontSize: "0.875rem",
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
  "& svg": {
    position: "relative",
    top: "5px",
    cursor: "pointer",
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:last-child td, &:last-child th": {
    border: 0,
  },
  "&:hover": {
    backgroundColor: theme.palette.action.selected,
    cursor: "pointer",
  },
}));

const ParentRow = styled(StyledTableRow)(({ theme }) => ({
  backgroundColor: "#f5f5f5 !important",
  fontWeight: "600",
  "&:hover": {
    backgroundColor: "#e8e8e8 !important",
  },
}));

const ChildRow = styled(StyledTableRow)(({ theme }) => ({
  backgroundColor: "#fafafa !important",
  "& td": {
    paddingLeft: "40px",
    fontSize: "13px",
  },
  "&:hover": {
    backgroundColor: "#f0f0f0 !important",
  },
}));

const NoDataContainer = styled(TableRow)({
  position: "relative",
  height: "50px",
});

const NoDataCell = styled(TableCell)({
  position: "absolute",
  right: "50%",
  borderBottom: "none",
  textAlign: "center",
  color: "#666",
  fontStyle: "italic",
});

// Table pagination actions component
function TablePaginationActions({ count, page, rowsPerPage, onPageChange }) {
  const theme = useTheme();

  const handleBackButtonClick = (event) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event) => {
    onPageChange(event, page + 1);
  };

  return (
    <div style={{ flexShrink: "0" }}>
      <IconButton
        onClick={handleBackButtonClick}
        disabled={page === 1}
        aria-label="previous page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowRight />
        ) : (
          <KeyboardArrowLeft />
        )}
      </IconButton>
      <IconButton
        onClick={handleNextButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage)}
        aria-label="next page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowLeft />
        ) : (
          <KeyboardArrowRight />
        )}
      </IconButton>
    </div>
  );
}

TablePaginationActions.propTypes = {
  count: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  page: PropTypes.number.isRequired,
  rowsPerPage: PropTypes.number.isRequired,
};

// Helper function to check if a row is mapped to Shopify
const isRowMapped = (row, isChild = false) => {
  if (isChild) {
    // For child variants, check multiple possible mapping indicators
    return (
      (row.shopifyVariantID && row.shopifyVariantID !== "") ||
      (row.shopify_product && row.shopify_product.shopifyVariantId && row.shopify_product.shopifyVariantId !== "")
    );
  } else {
    // For parent matrix products, check if they have shopifyProductID
    return row.shopifyProductID && row.shopifyProductID !== "";
  }
};

// Cell content renderer
const renderCellContent = (row, column, isChild = false) => {
  const value = row[column.id];

  switch (column.id) {
    case "expand":
      if (isChild) return null;
      return row.type === "MATRIX" && row.erply_product_variants?.length > 0 ? (
        <IconButton size="small">
          <ExpandMore />
        </IconButton>
      ) : null;

    case "priceWithVat":
    case "price":
      return value ? `$${parseFloat(value).toFixed(2)}` : "-";

    case "lastModified":
    case "added":
    case "created_at":
    case "lastUpdated":
      return value ? moment(value).format("DD MMM YYYY, h:mm a") : "-";

    case "status":
      return value

    case "type":
      return (
        <Chip
          label={value || "UNKNOWN"}
          color={value === "MATRIX" ? "primary" : "secondary"}
          size="small"
        />
      );

    case "mappingStatus":
      const isMapped = isRowMapped(row, isChild);
      return (
        <Chip
          label={isMapped ? "Mapped" : "Unmapped"}
          color={isMapped ? "success" : "warning"}
          size="small"
        />
      );

    case "url":
      return value ? (
        <Link href={value} target="_blank" rel="noopener noreferrer">
          <IconButton size="small">
            <Visibility />
          </IconButton>
        </Link>
      ) : "-";

    case "name":
      if (isChild && row.variationDescription) {
        try {
          const variations = JSON.parse(row.variationDescription);
          const variationText = variations.map(v => `${v.name}: ${v.value}`).join(", ");
          return (
            <Box>
              <div style={{ fontWeight: "500" }}>{value}</div>
              <div style={{ fontSize: "12px", color: "#666" }}>{variationText}</div>
            </Box>
          );
        } catch (e) {
          return value || "-";
        }
      }
      return value || "-";

    default:
      return value || "-";
  }
};

// Main table component
const MatrixProductTable = ({
  columns = [],
  rows = [],
  loading = false,
  sortable = true,
  currentColumn = "",
  direction = false,
  page = 1,
  total = 0,
  fromTable = 0,
  toTable = 0,
  rowsPerPage = 20,
  onSort,
  onChangePage,
  onChangeRowsPerPage,
  expandedRows = new Set(),
  setExpandedRows,
  showChildrenAlso = false,
}) => {
  const handleRowExpand = (productID) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(productID)) {
      newExpandedRows.delete(productID);
    } else {
      newExpandedRows.add(productID);
    }
    setExpandedRows(newExpandedRows);
  };

  // Filter and prepare data based on showChildrenAlso
  const prepareTableData = () => {
    let tableData = [];

    rows.forEach((row) => {
      const isMatrix = row.type === "MATRIX";
      const hasVariants = row.erply_product_variants && row.erply_product_variants.length > 0;
      const isExpanded = expandedRows.has(row.productID);

      // Always show parent rows
      tableData.push({
        ...row,
        isParent: isMatrix,
        isChild: false,
      });

      // Show children only if showChildrenAlso is enabled AND (expanded or showChildrenAlso forces all to show)
      if (showChildrenAlso && hasVariants && isExpanded) {
        row.erply_product_variants.forEach((variant) => {
          tableData.push({
            ...variant,
            isParent: false,
            isChild: true,
            parentProductID: row.productID,
          });
        });
      }
    });

    return tableData;
  };

  const tableData = prepareTableData();

  // Render table header
  const renderTableHeader = () => (
    <TableHead>
      <TableRow>
        {columns.map((column, index) => (
          <StyledTableCell
            key={column.id}
            onClick={sortable && onSort && column.id !== "expand" ? () => onSort(column.id) : undefined}
            style={{ 
              cursor: sortable && onSort && column.id !== "expand" ? "pointer" : "default",
              width: column.id === "expand" ? "50px" : "auto"
            }}
          >
            {column.name}
            {sortable && onSort && column.id !== "expand" && (
              <>
                {currentColumn === column.id ? (
                  direction ? (
                    <KeyboardArrowUp fontSize="small" />
                  ) : (
                    <KeyboardArrowDown fontSize="small" />
                  )
                ) : (
                  <UnfoldMore fontSize="small" />
                )}
              </>
            )}
          </StyledTableCell>
        ))}
      </TableRow>
    </TableHead>
  );

  // Render loading skeleton
  const renderLoadingSkeleton = () => (
    <TableBody>
      {Array.from({ length: 7 }).map((_, rowIndex) => (
        <TableRow key={rowIndex}>
          {columns.map((column) => (
            <StyledTableCell key={column.id}>
              <Skeleton variant="text" height={40} />
            </StyledTableCell>
          ))}
        </TableRow>
      ))}
    </TableBody>
  );

  return (
    <Paper sx={{ width: "100%", overflow: "hidden", marginBottom: "40px" }}>
      <TableContainer sx={{ maxHeight: 678 }}>
        <Table stickyHeader aria-label="matrix products table" sx={{ minWidth: 700 }}>
          {renderTableHeader()}
          {loading ? (
            renderLoadingSkeleton()
          ) : (
            <TableBody>
              {tableData.length > 0 ? (
                tableData.map((row, rowIndex) => {
                  const RowComponent = row.isParent ? ParentRow : row.isChild ? ChildRow : StyledTableRow;
                  
                  return (
                    <RowComponent
                      key={`${row.productID}-${rowIndex}`}
                      onClick={() => {
                        if (row.isParent && row.type === "MATRIX") {
                          handleRowExpand(row.productID);
                        }
                      }}
                      sx={{
                        backgroundColor: (() => {
                          const isMapped = isRowMapped(row, row.isChild);
                          if (isMapped) {
                            return row.isChild ? "#e8f5e8 !important" : "#d4edda !important";
                          }
                          return undefined;
                        })(),
                      }}
                    >
                      {columns.map((column, colIndex) => (
                        <StyledTableCell
                          key={column.id}
                          component={colIndex === 1 ? "th" : undefined}
                          scope={colIndex === 1 ? "row" : undefined}
                          onClick={(e) => {
                            if (column.id === "expand" && row.isParent && row.type === "MATRIX") {
                              e.stopPropagation();
                              handleRowExpand(row.productID);
                            }
                          }}
                        >
                          {column.id === "expand" && row.isParent && row.type === "MATRIX" ? (
                            <IconButton size="small">
                              {expandedRows.has(row.productID) ? <ExpandLess /> : <ExpandMore />}
                            </IconButton>
                          ) : (
                            renderCellContent(row, column, row.isChild)
                          )}
                        </StyledTableCell>
                      ))}
                    </RowComponent>
                  );
                })
              ) : (
                <NoDataContainer>
                  <NoDataCell colSpan={columns.length}>
                    No products found
                  </NoDataCell>
                </NoDataContainer>
              )}
            </TableBody>
          )}
          <TableFooter>
            <TableRow>
              <TablePagination
                rowsPerPageOptions={[20, 50, 70, 100]}
                rowsPerPage={rowsPerPage}
                page={page}
                count={total}
                slotProps={{ select: { native: true } }}
                labelDisplayedRows={() =>
                  `${fromTable || 0} - ${toTable || 0} of ${total}`
                }
                onPageChange={onChangePage}
                onRowsPerPageChange={onChangeRowsPerPage}
                ActionsComponent={TablePaginationActions}
              />
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
    </Paper>
  );
};

MatrixProductTable.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
    })
  ).isRequired,
  rows: PropTypes.array.isRequired,
  loading: PropTypes.bool,
  sortable: PropTypes.bool,
  currentColumn: PropTypes.string,
  direction: PropTypes.bool,
  page: PropTypes.number,
  total: PropTypes.number,
  fromTable: PropTypes.number,
  toTable: PropTypes.number,
  rowsPerPage: PropTypes.number,
  onSort: PropTypes.func,
  onChangePage: PropTypes.func,
  onChangeRowsPerPage: PropTypes.func,
  expandedRows: PropTypes.instanceOf(Set),
  setExpandedRows: PropTypes.func,
  showChildrenAlso: PropTypes.bool,
};

export default MatrixProductTable;
